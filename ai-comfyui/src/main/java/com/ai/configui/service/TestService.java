package com.ai.configui.service;

import com.ai.configui.event.PromptEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;

@Component
@RequestMapping("/test")
public class TestService {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @RequestMapping("/send")
    public void test() {

        PromptEvent event = new PromptEvent(this, "000011462641194572744371dbae2b59", "192.168.5.74:7860", PromptEvent.EventType.executing_completed);

        eventPublisher.publishEvent(event);

    }
}
