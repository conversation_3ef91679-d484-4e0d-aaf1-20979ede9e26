package com.ai.configui.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.ai.common.utils.uuid.IdUtils;
import com.ai.configui.api.NdClientApi;
import com.ai.configui.config.CustomConfig;
import com.ai.configui.domain.dto.BackendCallBackParams;
import com.ai.configui.domain.dto.ComfyR;
import com.ai.configui.domain.dto.HistoryVo;
import com.ai.configui.enums.ComfyStatusCodeEnum;
import com.ai.configui.enums.PromptStatus;
import com.ai.configui.enums.SensitiveMessage;
import com.ai.configui.event.PromptEvent;
import com.ai.configui.event.RMqMessage;
import com.ai.configui.util.ComfyUtil;
import com.ai.configui.util.FileUtils;
import com.ai.framework.cos.CosCommonService;
import com.ai.framework.mq.message.CommonMqMessage;
import com.ai.framework.mq.producer.NormalMessageProducer;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.ai.configui.event.PromptEvent.EventType.deal_result_error;
import static com.ai.configui.util.ComfyUtil.getQueryParamValue;
import static com.ai.framework.cos.CosCommonService.buildBatchUploadKeyPrefix;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CreateFileService {
    @Resource
    private NormalMessageProducer<ComfyR<BackendCallBackParams>> normalMessageProducer;
    // @Resource
    // private NormalMessageProducer<ComfyR<String>> normalMessageProducerCompress;

    @Resource
    private CosCommonService cosCommonService;
    @Resource
    private CustomConfig customConfig;
    @Value("${rocketmq.piclumen.create.tag:tag_piclumen_create_test}")
    private String tag;
    @Autowired
    private NdClientApi ndClientApi;

    // 创建一个线程池，用于处理图片压缩
    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(Runtime.getRuntime()
            .availableProcessors(), Runtime.getRuntime()
            .availableProcessors(), 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 处理失败消息
     *
     * @param hostAndPort
     * @param promptId
     * @param eventType
     */
    public void dealFailed(String hostAndPort, String promptId, PromptEvent.EventType eventType, String errorMessage) {
        String topic = customConfig.getTopicAddressMap()
                .get(hostAndPort);
        CommonMqMessage<ComfyR<BackendCallBackParams>> mqMessage = new RMqMessage<>(topic, tag, promptId);
        ComfyR<BackendCallBackParams> fail = null;

//        if (eventType.getCode().equals(PromptEvent.EventType.no_face_detected.getCode())) {
//            fail = ComfyR.withStatusCode(ComfyStatusCodeEnum.NO_FACE_DETECTED);
//        } else if (eventType.getCode().equals(PromptEvent.EventType.cache_expired.getCode())) {
//            fail = ComfyR.withStatusCode(ComfyStatusCodeEnum.TIME_OUT);
//        } else {
//            log.info("prompt: {} 处理失败 {}", promptId, eventType.getCode());
//            fail = ComfyR.withStatusCode(ComfyStatusCodeEnum.UNKNOWN);
//        }
        if (eventType == null || eventType.getCode() == null) {
            log.warn("Event type or its code is null, defaulting to UNKNOWN");
            fail = ComfyR.withStatusCode(ComfyStatusCodeEnum.UNKNOWN);
        } else {
            log.info("prompt switch: {} 处理失败 {}", promptId, eventType.getCode());
            switch (eventType) {
                case no_face_detected:
                    fail = ComfyR.withStatusCode(ComfyStatusCodeEnum.NO_FACE_DETECTED);
                    break;
                case out_of_memory_gpu:
                    fail = ComfyR.withStatusCode(ComfyStatusCodeEnum.OUT_OF_MEMORY_GPU);
                    break;
                case cache_expired:
                    fail = ComfyR.withStatusCode(ComfyStatusCodeEnum.TIME_OUT);
                    break;
                case execution_error:
                    fail = ComfyR.withStatusCodeMsg(ComfyStatusCodeEnum.EXECUTION_ERROR, errorMessage);
                    break;
                case execution_interrupted:
                    fail = ComfyR.withStatusCode(ComfyStatusCodeEnum.EXECUTION_INTERRUPTED);
                    break;
                case not_fund_history:
                    fail = ComfyR.withStatusCode(ComfyStatusCodeEnum.NOT_FUND_HISTORY);
                    break;
                case deal_result_error:
                    fail = ComfyR.withStatusCode(ComfyStatusCodeEnum.DEAL_RESULT_ERROR);
                    break;
                default:
                    fail = ComfyR.withStatusCode(ComfyStatusCodeEnum.UNKNOWN);
                    break;
            }
        }

        BackendCallBackParams data = new BackendCallBackParams();
        fail.setData(data);
        BackendCallBackParams.Result result = new BackendCallBackParams.Result(promptId);
        result.setPrompt_status(PromptStatus.failure.getValue());
        data.setResult(result);
        mqMessage.setMessage(fail);
        normalMessageProducer.syncSend(mqMessage);
    }

    /**
     * 处理成功消息
     *
     * @param promptId
     * @param hostAndPort
     * @param historyVo
     */
    public void dealSuccess(String promptId, String hostAndPort, HistoryVo historyVo) {
        int type = historyVo.getType();
        if (type == 0) {
            List<String> history = historyVo.getFileNames();
            List<BackendCallBackParams.ImgMessage> imgMessageList = new ArrayList<>();
            if (CollUtil.isNotEmpty(history)) {
                imgMessageList = getImgMessages(history, promptId, hostAndPort);
            }
            if ((imgMessageList == null || CollUtil.isEmpty(imgMessageList))/* && (textList == null || CollUtil.isEmpty(textList))*/) {
                log.info("prompt: {} 图片处理失败", promptId);
                dealFailed(hostAndPort, promptId, deal_result_error, null);
                return;
            }

            BackendCallBackParams promptMqVo = new BackendCallBackParams();
            BackendCallBackParams.Result result = new BackendCallBackParams.Result();
            promptMqVo.setResult(result);
            result.setPrompt_id(promptId);
            result.setPrompt_status(PromptStatus.success.getValue());

            result.setImgMessageList(imgMessageList);
//        result.setTextList(textList);
            String topic = customConfig.getTopicAddressMap()
                    .get(hostAndPort);
            CommonMqMessage<ComfyR<BackendCallBackParams>> mqMessage = new RMqMessage<>(topic, tag, promptId);
            mqMessage.setMessage(ComfyR.success(promptMqVo));
            normalMessageProducer.syncSend(mqMessage);
        } else if (type == 1) {
            log.info("prompt: {} 视频处理", promptId);
            // 1. 下载视频
            // 2. ffmpeg 取首帧 尾帧 鉴黄
            // 3. 上传到cos
        }

        //
        // 压缩
        // try {
        //     CommonMqMessage<ComfyR<String>> mqMessage2 = new RMqMessage<>(topic, tag, promptId);
        //     mqMessage2.setMessage(ComfyR.success(compressAndEncodeToBase64(mapper.writeValueAsString(promptMqVo))));
        //     normalMessageProducerCompress.syncSend(mqMessage2);
        // } catch (IOException e) {
        //     throw new RuntimeException(e);
        // }
    }


    /**
     * 批量上传
     *
     * @param history
     * @param promptId
     * @param hostAndPort
     * @return
     */
    private List<BackendCallBackParams.ImgMessage> getImgMessages(List<String> history, String promptId, String hostAndPort) {
        List<CompletableFuture<BackendCallBackParams.ImgMessage>> futures = new ArrayList<>(history.size() * 4);

        long startTimestamp = System.currentTimeMillis();
        log.info("开始处理历史图片:{} size:{}", promptId, history.size());
        String cosPreFix = buildBatchUploadKeyPrefix();
        String cosPath = cosCommonService.getConfig().getCosAccelerateDomain() + cosPreFix;
        List<File> files = Collections.synchronizedList(new ArrayList<>(history.size() * 4));
        for (String imgUrl : history) {
            CompletableFuture<BackendCallBackParams.ImgMessage> future = CompletableFuture.supplyAsync(() -> {
                log.info("start 开始处理图片:{} {}", imgUrl, promptId);
                BackendCallBackParams.ImgMessage imgMessage = new BackendCallBackParams.ImgMessage();
                String fileName = getUrlParamValue(imgUrl);
                if (fileName == null) {
                    fileName = IdUtils.simpleUUID();
                }
                File tempFile = null;
                // 生成缩略图
                File thumbnailFile15 = null;
                File thumbnailFile30 = null;
                File thumbnailFile30_2 = null;
                File thumbnailFile70 = null;
                // 生成高清缩略图
                File highThumbnailFile90 = null;
                try {
                    // tempFile = File.createTempFile("temp-", fileName);
                    tempFile = FileUtil.newFile("/comfy/" + promptId + "/" + IdUtils.simpleUUID() + ".png");
                    try {
                        String queryParamValue = getQueryParamValue(imgUrl, "filename");
                        if (StrUtil.isNotBlank(queryParamValue)) {
                            log.info("尝试从本地获取图片:{}", queryParamValue);
                            tempFile = ComfyUtil.findImgByPortAndNameAndCopy(hostAndPort.split(":")[1], queryParamValue, tempFile.getAbsolutePath());
                        }
                    } catch (Exception e) {
                        log.error("本地获取图片下载失败", e);
                    }
                    if (tempFile == null) {
                        tempFile = FileUtil.newFile("/comfy/" + promptId + "/" + IdUtils.simpleUUID() + ".png");
                        HttpUtil.downloadFile(imgUrl, tempFile);
                    }
                    if (tempFile == null || tempFile.length() == 0) {
                        log.error("图片下载失败:{}", imgUrl);
                        return null;
                    }
                    // 对图片进行鉴黄处理
                    log.info("对图片进行鉴黄处理start,promptId:{}，图片名称:{}", promptId, fileName);
                    String ndResult = ndPicture(tempFile);
                    log.info("对图片进行鉴黄处理end,promptId:{}，图片名称:{}", promptId, fileName);

                    // 如果是有鉴黄信息，则对图片进行标记
                    if (StrUtil.isNotBlank(ndResult)) {
                        // 马赛克图片，原始的和缩略图公用一张马赛克图片
                        tempFile = FileUtils.createMosaic(tempFile);
                        imgMessage.setSensitive(SensitiveMessage.NSFW.getValue());
                        buildImageSize(tempFile, imgMessage);
                        String signedUrl = cosCommonService.uploadToOss(tempFile);
                        log.info("图片上传到腾讯云完成,promptId:{}，图片名称:{}", promptId, signedUrl);
                        imgMessage.setImg_url(signedUrl);
                        imgMessage.setThumbnail_url(signedUrl);
                        imgMessage.setHigh_thumbnail_url(signedUrl);
                        return imgMessage;
                    }
                    thumbnailFile15 = FileUtils.createThumbnail15(tempFile, promptId);
                    thumbnailFile70 = FileUtils.createThumbnail70(tempFile, promptId);
                    thumbnailFile30 = FileUtils.createThumbnail30(tempFile, promptId);
                    highThumbnailFile90 = FileUtils.createHighThumbnail90(tempFile, promptId);
                    buildImageSize(highThumbnailFile90, imgMessage);
                    imgMessage.setSize(highThumbnailFile90.length());
                    imgMessage.setImg_url(cosPath + highThumbnailFile90.getName());
                    imgMessage.setHigh_thumbnail_url(cosPath + highThumbnailFile90.getName());
                    imgMessage.setThumbnail_url(cosPath + thumbnailFile70.getName());
                    imgMessage.setHighMiniUrl(cosPath + thumbnailFile30.getName());
                    imgMessage.setMiniThumbnailUrl(cosPath + thumbnailFile15.getName());
                    // copy to new File
                    thumbnailFile30_2 = new File("/image_nsfw/" + promptId + "/" + thumbnailFile30.getName());
                    FileUtil.copyContent(thumbnailFile30, thumbnailFile30_2, true);
//                    Integer i = ndClientApi.nsfwProcess(thumbnailFile30_2);
//                    imgMessage.setNsfw(i);
//                    log.info("promptId:{}，上传图片到cos图片名称url: {} nsfw: {}", promptId, imgMessage.getImg_url(), i);
                    log.info("end 开始处理图片 :{} {}", imgUrl, promptId);
                    return imgMessage;
                } catch (Exception e) {
                    log.error("dealSuccess 发生错误, {}", promptId, e);
                    return null;
                } finally {
//                    files.add(tempFile);
                    files.add(highThumbnailFile90);
                    files.add(thumbnailFile70);
                    files.add(thumbnailFile30);
                    files.add(thumbnailFile15);
                    FileUtil.del(tempFile);
                    FileUtil.del(thumbnailFile30_2);
                }
            }, EXECUTOR);
            futures.add(future);
        }
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .join();
            log.info("开始上传历史图片到cos, {}", promptId);
            long t1 = System.currentTimeMillis();
            File dirPath = new File("/comfy/" + promptId + "/");
            if (!dirPath.exists() || !dirPath.isDirectory()) {
                log.error("上传历史图片到cos失败，没有获取到图片, promptId:{}", promptId);
                return null;
            }
            String[] list = dirPath.list();
            if (list != null) {
                log.info("开始上传历史图片到cos, {} {}", list.length, String.join(",", list));
            }
            cosCommonService.uploadDirectory(cosPreFix, dirPath);
            log.info("历史图片上传到cos完成，promptId:{} 耗时: {}s", promptId, (System.currentTimeMillis() - t1) / 1000D);
        } catch (Exception e) {
            log.error("上传历史图片到cos失败 {}", promptId, e);
            return null;
        } finally {
            FileUtil.del(new File("/comfy/" + promptId + "/"));
            FileUtil.del(new File("/image_nsfw/" + promptId + "/"));
        }

        log.info("图片处理完成，开始发送消息，promptId:{} 耗时: {}s", promptId, (System.currentTimeMillis() - startTimestamp) / 1000D);

        List<BackendCallBackParams.ImgMessage> imgMessageList = new ArrayList<>(history.size());
        for (CompletableFuture<BackendCallBackParams.ImgMessage> future : futures) {
            try {
                BackendCallBackParams.ImgMessage imgMessage = future.get();
                if (imgMessage == null) {
                    return null;
                }
                imgMessageList.add(imgMessage);
            } catch (Exception e) {
                log.error("dealSuccess ImgMessage error {}", promptId, e);
                return null;
            }
        }
        return imgMessageList;
    }

    /*
     */

    /**
     * 多线程上传
     *
     * @return
     *//*

    private List<BackendCallBackParams.ImgMessage> getImgMessages2(List<String> history, String promptId, String hostAndPort) {
        List<CompletableFuture<BackendCallBackParams.ImgMessage>> futures = new ArrayList<>(history.size() * 3);

        long startTimestamp = System.currentTimeMillis();
        log.info("开始处理历史图片");
        for (String imgUrl : history) {
            CompletableFuture<BackendCallBackParams.ImgMessage> future = CompletableFuture.supplyAsync(() -> {
                log.info("start 开始处理图片 :{}", imgUrl);
                BackendCallBackParams.ImgMessage imgMessage = new BackendCallBackParams.ImgMessage();
                String fileName = getUrlParamValue(imgUrl);
                if (fileName == null) {
                    fileName = IdUtils.simpleUUID();
                }
                File tempFile = null;
                // 生成缩略图
                File thumbnailFile = null;
                // 生成高清缩略图
                File highThumbnailFile = null;
                try {
                    tempFile = File.createTempFile("temp-", fileName);
                    HttpUtil.downloadFile(imgUrl, tempFile);
                    // 对图片进行鉴黄处理
                    log.info("对图片进行鉴黄处理start,promptId:{}，图片名称:{}", promptId, fileName);
                    String ndResult = ndPicture(tempFile);
                    log.info("对图片进行鉴黄处理end,promptId:{}，图片名称:{}", promptId, fileName);

                    // 如果是有鉴黄信息，则对图片进行标记
                    if (StrUtil.isNotBlank(ndResult)) {
                        // 马赛克图片，原始的和缩略图公用一张马赛克图片
                        tempFile = FileUtils.createMosaic(tempFile);
                        imgMessage.setSensitive(SensitiveMessage.NSFW.getValue());
                        buildImageSize(tempFile, imgMessage);
                        String signedUrl = cosCommonService.uploadToOss(tempFile);
                        log.info("图片上传到腾讯云完成,promptId:{}，图片名称:{}", promptId, signedUrl);
                        imgMessage.setImg_url(signedUrl);
                        imgMessage.setThumbnail_url(signedUrl);
                        imgMessage.setHigh_thumbnail_url(signedUrl);
                        return imgMessage;
                    }
                    long t0 = System.currentTimeMillis();
                    thumbnailFile = FileUtils.createThumbnail(tempFile);
                    highThumbnailFile = FileUtils.createHighThumbnail(tempFile);
                    buildImageSize(highThumbnailFile, imgMessage);

                    long t1 = System.currentTimeMillis();
                    log.info("图片压缩处理耗时:{}s", (t1 - t0) / 1000D);
                    String signedUrl = cosCommonService.uploadToOss(tempFile);
                    log.info("图片上传到腾讯云完成,promptId:{}，图片名称:{}", promptId, signedUrl);
                    // 保存缩略图到腾讯云
                    String thumbnailUrl = cosCommonService.uploadToOss(thumbnailFile);
                    log.info("缩略图上传到腾讯云完成,promptId:{}，图片名称:{}", promptId, thumbnailUrl);
                    // 保存高清缩略图到腾讯云
                    String highThumbnailFileUrl = cosCommonService.uploadToOss(highThumbnailFile);
                    log.info("高清缩略图上传到腾讯云完成,promptId:{}，图片名称:{}", promptId, highThumbnailFileUrl);
                    log.info("图片上传耗时:{}s", (System.currentTimeMillis() - t1 / 1000D));
                    imgMessage.setSize(FileUtil.size(tempFile));
                    imgMessage.setImg_url(signedUrl);
                    imgMessage.setThumbnail_url(thumbnailUrl);
                    imgMessage.setHigh_thumbnail_url(highThumbnailFileUrl);
                    log.info("end 开始处理图片 :{}", imgUrl);

                } catch (Exception e) {
                    log.error("dealSuccess error occurred", e);
                } finally {
                    FileUtil.del(tempFile);
                    FileUtil.del(thumbnailFile);
                    FileUtil.del(highThumbnailFile);
                }
                return imgMessage;
            }, EXECUTOR);
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .join();

        log.info("图片处理完成，开始发送消息，promptId:{} 耗时: {}s", promptId, (System.currentTimeMillis() - startTimestamp) / 1000D);

        List<BackendCallBackParams.ImgMessage> imgMessageList = new ArrayList<>(history.size());
        for (CompletableFuture<BackendCallBackParams.ImgMessage> future : futures) {
            BackendCallBackParams.ImgMessage imgMessage = null;
            try {
                imgMessage = future.get();
            } catch (Exception e) {
                dealFailed(hostAndPort, promptId, deal_result_error, null);
                log.error("dealSuccess ImgMessage error", e);
                return null;
            }
            imgMessageList.add(imgMessage);
        }
        return imgMessageList;
    }

    private List<BackendCallBackParams.ImgMessage> getImgMessages3(List<String> history, String promptId, String hostAndPort) {
        List<CompletableFuture<BackendCallBackParams.ImgMessage>> futures = new ArrayList<>(history.size() * 3);

        long startTimestamp = System.currentTimeMillis();
        log.info("开始处理历史图片");
        for (String imgUrl : history) {
            CompletableFuture<BackendCallBackParams.ImgMessage> future = CompletableFuture.supplyAsync(() -> {
                log.info("start 开始处理图片 :{}", imgUrl);
                BackendCallBackParams.ImgMessage imgMessage = new BackendCallBackParams.ImgMessage();
                String fileName = getUrlParamValue(imgUrl);
                if (fileName == null) {
                    fileName = IdUtils.simpleUUID();
                }
                File tempFile = null;
                // 生成缩略图
                File thumbnailFile = null;
                // 生成高清缩略图
                File highThumbnailFile = null;
                try {
                    tempFile = File.createTempFile("temp-", fileName);
                    HttpUtil.downloadFile(imgUrl, tempFile);
                    // 对图片进行鉴黄处理
                    log.info("对图片进行鉴黄处理start,promptId:{}，图片名称:{}", promptId, fileName);
                    String ndResult = ndPicture(tempFile);
                    log.info("对图片进行鉴黄处理end,promptId:{}，图片名称:{}", promptId, fileName);

                    // 如果是有鉴黄信息，则对图片进行标记
                    if (StrUtil.isNotBlank(ndResult)) {
                        // 马赛克图片，原始的和缩略图公用一张马赛克图片
                        tempFile = FileUtils.createMosaic(tempFile);
                        imgMessage.setSensitive(SensitiveMessage.NSFW.getValue());
                        buildImageSize(tempFile, imgMessage);
                        String signedUrl = cosCommonService.uploadToOss(tempFile);
                        log.info("图片上传到腾讯云完成,promptId:{}，图片名称:{}", promptId, signedUrl);
                        imgMessage.setImg_url(signedUrl);
                        imgMessage.setThumbnail_url(signedUrl);
                        imgMessage.setHigh_thumbnail_url(signedUrl);
                        return imgMessage;
                    }
                    long t0 = System.currentTimeMillis();
                    thumbnailFile = FileUtils.createThumbnail(tempFile);
                    highThumbnailFile = FileUtils.createHighThumbnail(tempFile);
                    buildImageSize(highThumbnailFile, imgMessage);

                    long t1 = System.currentTimeMillis();
                    log.info("图片压缩处理耗时:{}s", (t1 - t0) / 1000D);
                    CompletableFuture<String> signedUrl = cosCommonService.uploadToOssAsync(tempFile);
                    log.info("图片上传到腾讯云完成,promptId:{}，图片名称:{}", promptId, signedUrl);
                    // 保存缩略图到腾讯云
                    CompletableFuture<String> thumbnailUrl = cosCommonService.uploadToOssAsync(thumbnailFile);
                    log.info("缩略图上传到腾讯云完成,promptId:{}，图片名称:{}", promptId, thumbnailUrl);
                    // 保存高清缩略图到腾讯云
                    CompletableFuture<String> highThumbnailFileUrl = cosCommonService.uploadToOssAsync(highThumbnailFile);
                    log.info("高清缩略图上传到腾讯云完成,promptId:{}，图片名称:{}", promptId, highThumbnailFileUrl);
                    log.info("图片上传耗时:{}s", (System.currentTimeMillis() - t1 / 1000D));
                    imgMessage.setSize(tempFile.length());
                    imgMessage.setImg_url(signedUrl.get());
                    imgMessage.setThumbnail_url(thumbnailUrl.get());
                    imgMessage.setHigh_thumbnail_url(highThumbnailFileUrl.get());
                    log.info("end 开始处理图片 :{}", imgUrl);

                } catch (Exception e) {
                    log.error("dealSuccess", e);
                } finally {
                    FileUtil.del(tempFile);
                    FileUtil.del(thumbnailFile);
                    FileUtil.del(highThumbnailFile);
                }
                return imgMessage;
            }, EXECUTOR);
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .join();

        log.info("图片处理完成，开始发送消息，promptId:{} 耗时: {}s", promptId, (System.currentTimeMillis() - startTimestamp) / 1000D);

        List<BackendCallBackParams.ImgMessage> imgMessageList = new ArrayList<>(history.size());
        for (CompletableFuture<BackendCallBackParams.ImgMessage> future : futures) {
            BackendCallBackParams.ImgMessage imgMessage = null;
            try {
                imgMessage = future.get();
            } catch (Exception e) {
                dealFailed(hostAndPort, promptId, deal_result_error, null);
                log.error("dealSuccess ImgMessage error", e);
                return null;

            }
            imgMessageList.add(imgMessage);
        }
        return imgMessageList;
    }
*/
    private void buildImageSize(File imageFile, BackendCallBackParams.ImgMessage imgMessage) {
        if (imageFile == null || !imageFile.exists() || !imageFile.canRead()) {
            log.error("文件不存在或无法读取: {}", imageFile != null ? imageFile.getAbsolutePath() : "");
            return;
        }
        try {
            // 读取图片文件
            BufferedImage image = ImageIO.read(imageFile);
            if (image != null) {
                // 判断是否成功读取图片
                int width = image.getWidth();
                imgMessage.setWidth(width);
                int height = image.getHeight();
                imgMessage.setHeight(height);
            } else {
                log.info("无法读取文件，可能不是有效的图片格式");
            }
        } catch (IOException e) {
            log.error("获取图片宽高失败", e);
        }
    }


    private String ndPicture(File file) throws IOException {
        return ndClientApi.ndPicture(file);
    }


    private String getUrlParamValue(String urlStr) {
        try {
            URL url = new URL(urlStr);
            String query = url.getQuery();
            for (String param : query.split("&")) {
                String[] pair = param.split("=");
                String key = URLDecoder.decode(pair[0], "UTF-8");
                String value = URLDecoder.decode(pair[1], "UTF-8");
                if ("filename".equals(key)) {
                    return value;
                }
            }
        } catch (Exception e) {
            log.error("获取参数失败 ", e);
        }
        return null;
    }
}
