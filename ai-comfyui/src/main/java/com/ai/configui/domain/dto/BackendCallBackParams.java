package com.ai.configui.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BackendCallBackParams {

    @Schema(description = "任务成功信息")
    private Result result;


    @ToString
    @Data
    public static class Result {

        @Schema(description = "生图prompt_id")
        private String prompt_id;

        @Schema(description = "当前生图任务执行状态")
        private String prompt_status;

        @Schema(description = "生图成功后地址")
        private List<ImgMessage> imgMessageList;

//        @Schema(description = "生图成功后文本")
//        private List<String> textList;

        // private long startTimeStamp;
        // private long endTimeStamp;

        public Result() {
        }

        public Result(String prompt_id) {
            this.prompt_id = prompt_id;
        }
    }

    @ToString
    @Data
    public static class ImgMessage {

        @Schema(description = "原始图片生成后腾讯云地址")
        private String img_url;

        @Schema(description = "图片大小")
        private Long size;

        @Schema(description = "缩略图生成后腾讯云地址")
        private String thumbnail_url;

        @Schema(description = "高清缩略图生成后腾讯云地址")
        private String high_thumbnail_url;

        @Schema(description = "mini图")
        private String miniThumbnailUrl;

        @Schema(description = "30% 高清图")
        private String highMiniUrl;

        @Schema(description = "敏感信息")
        private String sensitive;

        @Schema(description = "图片名称")
        private Integer nsfw;

        @Schema(description = "原始图片真实宽")
        private int width;

        @Schema(description = "原始图片高")
        private int height;

        @Schema(description = "视频文件链接")
        private String video_url;

    }
}
