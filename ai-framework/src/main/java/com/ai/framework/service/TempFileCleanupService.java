package com.ai.framework.service;

import com.ai.framework.util.SafeFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.concurrent.TimeUnit;

/**
 * 临时文件清理服务
 * 定期清理可能遗留的临时文件
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@ConditionalOnProperty(value = "temp-file-cleanup.enabled", havingValue = "true", matchIfMissing = true)
public class TempFileCleanupService {

    /**
     * 临时文件目录
     */
    private static final String[] TEMP_DIRECTORIES = {
            "image_process",
            System.getProperty("java.io.tmpdir") + File.separator + "image_process",
            "temp",
            "tmp"
    };

    /**
     * 文件过期时间（小时）
     */
    private static final int FILE_EXPIRE_HOURS = 2;

    /**
     * 每小时执行一次清理
     */
//    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void cleanupTempFiles() {
        log.info("开始执行临时文件清理任务...");

        long startTime = System.currentTimeMillis();
        int totalCleaned = 0;

        for (String tempDir : TEMP_DIRECTORIES) {
            try {
                int cleaned = cleanupDirectory(tempDir);
                totalCleaned += cleaned;
                log.debug("清理目录 {} 完成，删除文件数: {}", tempDir, cleaned);
            } catch (Exception e) {
                log.error("清理目录 {} 时发生异常", tempDir, e);
            }
        }

        long duration = System.currentTimeMillis() - startTime;
        log.info("临时文件清理任务完成，总删除文件数: {}，耗时: {}ms", totalCleaned, duration);
    }

    /**
     * 清理指定目录
     */
    private int cleanupDirectory(String dirPath) {
        File directory = new File(dirPath);

        if (!directory.exists() || !directory.isDirectory()) {
            log.debug("目录不存在或不是目录: {}", dirPath);
            return 0;
        }

        log.debug("开始清理目录: {}", dirPath);
        return cleanupDirectoryRecursive(directory);
    }

    /**
     * 递归清理目录
     */
    private int cleanupDirectoryRecursive(File directory) {
        int cleanedCount = 0;

        File[] files = directory.listFiles();
        if (files == null) {
            return 0;
        }

        long expireTime = System.currentTimeMillis() - TimeUnit.HOURS.toMillis(FILE_EXPIRE_HOURS);

        for (File file : files) {
            try {
                if (file.isDirectory()) {
                    // 递归清理子目录
                    cleanedCount += cleanupDirectoryRecursive(file);

                    // 如果子目录为空且过期，删除它
                    if (isDirectoryEmpty(file) && file.lastModified() < expireTime) {
                        if (SafeFileUtil.safeDelete(file)) {
                            log.debug("删除空目录: {}", file.getAbsolutePath());
                            cleanedCount++;
                        }
                    }
                } else {
                    // 清理过期文件
                    if (shouldDeleteFile(file, expireTime)) {
                        if (SafeFileUtil.safeDelete(file)) {
                            log.debug("删除过期文件: {}", file.getAbsolutePath());
                            cleanedCount++;
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("处理文件 {} 时发生异常", file.getAbsolutePath(), e);
            }
        }

        return cleanedCount;
    }

    /**
     * 判断是否应该删除文件
     */
    private boolean shouldDeleteFile(File file, long expireTime) {
        // 检查文件是否过期
        if (file.lastModified() >= expireTime) {
            return false;
        }

        // 检查文件名模式，只删除明确的临时文件
        String fileName = file.getName().toLowerCase();

        // 图片处理相关的临时文件
        if (fileName.contains("temp") ||
                fileName.contains("tmp") ||
                fileName.startsWith("image_") ||
                fileName.contains("process") ||
                fileName.endsWith(".tmp") ||
                fileName.endsWith(".temp")) {
            return true;
        }

        // 检查文件扩展名
        String extension = getFileExtension(fileName);
        if (isTemporaryFileExtension(extension)) {
            return true;
        }

        // 检查文件大小，删除异常大的文件（可能是处理失败的文件）
        long fileSize = file.length();
        if (fileSize > 100 * 1024 * 1024) { // 100MB
            log.warn("发现异常大的临时文件: {} ({}MB)", file.getAbsolutePath(), fileSize / 1024 / 1024);
            return true;
        }

        return false;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1);
        }
        return "";
    }

    /**
     * 判断是否为临时文件扩展名
     */
    private boolean isTemporaryFileExtension(String extension) {
        String[] tempExtensions = {
                "tmp", "temp", "bak", "cache", "log", "lock"
        };

        for (String tempExt : tempExtensions) {
            if (tempExt.equals(extension)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查目录是否为空
     */
    private boolean isDirectoryEmpty(File directory) {
        if (!directory.isDirectory()) {
            return false;
        }

        File[] files = directory.listFiles();
        return files == null || files.length == 0;
    }

    /**
     * 手动触发清理（用于测试或紧急清理）
     */
    public void manualCleanup() {
        log.info("手动触发临时文件清理...");
        cleanupTempFiles();
    }

    /**
     * 获取临时文件统计信息
     */
    public TempFileStats getTempFileStats() {
        TempFileStats stats = new TempFileStats();

        for (String tempDir : TEMP_DIRECTORIES) {
            try {
                File directory = new File(tempDir);
                if (directory.exists() && directory.isDirectory()) {
                    collectStats(directory, stats);
                }
            } catch (Exception e) {
                log.error("收集目录 {} 统计信息时发生异常", tempDir, e);
            }
        }

        return stats;
    }

    /**
     * 收集统计信息
     */
    private void collectStats(File directory, TempFileStats stats) {
        File[] files = directory.listFiles();
        if (files == null) {
            return;
        }

        for (File file : files) {
            if (file.isDirectory()) {
                stats.directoryCount++;
                collectStats(file, stats);
            } else {
                stats.fileCount++;
                stats.totalSize += file.length();

                long expireTime = System.currentTimeMillis() - TimeUnit.HOURS.toMillis(FILE_EXPIRE_HOURS);
                if (file.lastModified() < expireTime) {
                    stats.expiredFileCount++;
                    stats.expiredFileSize += file.length();
                }
            }
        }
    }

    /**
     * 临时文件统计信息
     */
    public static class TempFileStats {
        public int fileCount = 0;
        public int directoryCount = 0;
        public long totalSize = 0;
        public int expiredFileCount = 0;
        public long expiredFileSize = 0;

        @Override
        public String toString() {
            return String.format("TempFileStats{文件数=%d, 目录数=%d, 总大小=%dMB, 过期文件数=%d, 过期文件大小=%dMB}",
                    fileCount, directoryCount, totalSize / 1024 / 1024,
                    expiredFileCount, expiredFileSize / 1024 / 1024);
        }
    }
}
