package com.ai.framework.cos;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.ai.common.enums.UploadType;
import com.ai.common.utils.uuid.IdUtils;
import com.ai.framework.config.cos.TencentCloudStorageConfig;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.transfer.Copy;
import com.qcloud.cos.transfer.MultipleFileUpload;
import com.qcloud.cos.transfer.ObjectMetadataProvider;
import com.qcloud.cos.transfer.TransferManager;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 操作 cos common类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CosCommonService {
    @Resource(name = "accelerateCosClient")
    private COSClient baseCosClient;
    @Resource
    @Getter
    private TencentCloudStorageConfig config;
    @Resource(name = "accelerateTransferManager")
    private TransferManager transferManager;

    private static final ObjectMetadataProvider metadataProvider = (file, metadata) -> {
        // 100 年 不变
        metadata.setCacheControl("max-age=315360000,immutable");
    };

    private static final ObjectMetadata metadata = new ObjectMetadata();

    static {
        metadata.setCacheControl("max-age=315360000,immutable");
    }

    // 异步上传线程池

    private static final ExecutorService threadPoolExecutor = Executors.newFixedThreadPool(12);


    /**
     * 桶/日期/userId
     *
     * @param file
     * @return
     */
    public String uploadToOss(File file) {
        return uploadToOssWithType(file, UploadType.normal.getValue());
    }

    public CompletableFuture<String> uploadToOssAsync(File file) {
        CompletableFuture<String> uCompletableFuture = CompletableFuture.supplyAsync(() -> {
            return uploadToOssWithType(file, UploadType.normal.getValue());
        }, threadPoolExecutor);
        return uCompletableFuture;

    }

    public String uploadToOssWithType(File file, String type) {
        String name = file.getName();
        String key = buildFileNameKey(name.split("\\.")[1], type);
        uploadTcNoAccelerate(key, file);
        return config.getCosAccelerateDomain() + key;
    }

    private PutObjectResult uploadTcNoAccelerate(String key, File file) {
        PutObjectRequest putObjectRequest = new PutObjectRequest(config.getBucketName(), key, file);
        putObjectRequest.setMetadata(metadata);
        return baseCosClient.putObject(putObjectRequest);
    }


    public static String getFilePath(String fullPathUrl) {
        String path = URLUtil.getPath(fullPathUrl);
        if (path.startsWith("/")) {
            return path.substring(1);
        }
        return path;
    }

    public static String buildFileNameKey(String fileExt, String type) {
        if (StrUtil.isBlank(type)) {
            type = UploadType.normal.getValue();
        }
        StringBuilder sb = new StringBuilder("/" + type + "/");
        LocalDateTime now = LocalDateTime.now();
        String path = sb.append(DateUtil.format(now, "yyyyMMdd"))
                .append("/")
                .append(DateUtil.format(now, "HH"))
                .append("/")
                .append(IdUtils.randomUUID())
                .append(".")
                .append(fileExt)
                .toString();
        return path;
    }

    /**
     * 批量上传文件路径前缀：/upload/202305/05/
     *
     * @return
     */
    public static String buildBatchUploadKeyPrefix() {
        StringBuilder sb = new StringBuilder("/" + UploadType.normal.getValue() + "/");
        LocalDateTime now = LocalDateTime.now();
        String path = sb.append(DateUtil.format(now, "yyyyMMdd"))
                .append("/")
                .append(DateUtil.format(now, "HH"))
                .append("/")
                .toString();
        return path;
    }

    /**
     * 批量上传文件路径前缀：/upload/202305/05/
     *
     * @return
     */
    public static String buildBatchUploadKeyPrefixForVideo() {
        StringBuilder sb = new StringBuilder("/" + UploadType.video.getValue() + "/");
        LocalDateTime now = LocalDateTime.now();
        String path = sb.append(DateUtil.format(now, "yyyyMMdd"))
                .append("/")
                .append(DateUtil.format(now, "HH"))
                .append("/")
                .toString();
        return path;
    }

    /**
     * @param
     * @param dirPath 要上传的文件夹的绝对路径
     */
    public String uploadDirectory(String cosPath, File dirPath) throws Exception {
        // 目录下文件为空 则直接返回
        if (dirPath == null || !dirPath.exists() || !dirPath.isDirectory()) {
            log.error("上传目录失败，目录不存在:{}", dirPath);
            return null;
        }
        //  cosPath 设置文件上传到 bucket 之后的前缀目录，设置为 “”，表示上传到 bucket 的根目录
        //  cosPath = buildBatchUploadKeyPrefix();
        // 是否递归上传目录下的子目录，如果是 true，子目录下的文件也会上传，且cos上会保持目录结构
        boolean recursive = false;
        // 返回一个异步结果Upload, 可同步的调用waitForUploadResult等待upload结束, 成功返回UploadResult, 失败抛出异常.

        MultipleFileUpload upload = transferManager.uploadDirectory(config.getBucketName(), cosPath, dirPath, recursive, metadataProvider);
        // 可以选择查看上传进度
        // showTransferProgress(upload);
        // 或者阻塞等待完成
        upload.waitForCompletion();
        log.info("上传完成 {} ", cosPath);

        return config.getCosAccelerateDomain() + cosPath;

    }
}
