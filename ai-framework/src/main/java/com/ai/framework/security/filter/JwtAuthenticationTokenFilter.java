package com.ai.framework.security.filter;

import java.io.IOException;
import java.io.PrintWriter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.StrUtil;
import com.ai.common.core.domain.model.ClientInfo;
import com.ai.framework.web.service.PlatformAuthenticator;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import com.ai.framework.web.service.TokenService;

/**
 * token过滤器 验证token有效性
 *
 * <AUTHOR>
 */
@Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter implements ApplicationContextAware {
    @Autowired
    PlatformAuthenticator platformAuthenticator;
    // @Autowired
    // private TokenService tokenService;

    // public JwtAuthenticationTokenFilter(TokenService jwtTokenProvider) {
    //     this.tokenService = jwtTokenProvider;
    // }
    private static ApplicationContext applicationContext;

    /**
     * 检查当前环境是否为生产环境（prod）
     *
     * @return 如果当前环境为 prod 返回 true，否则返回 false
     */
    public static boolean isProdEnvironment() {
        Environment environment = applicationContext.getEnvironment();
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("prod".equalsIgnoreCase(profile)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isTestEnvironment() {
        Environment environment = applicationContext.getEnvironment();
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("test".equalsIgnoreCase(profile) || "dev".equalsIgnoreCase(profile) || "prestg".equalsIgnoreCase(profile)) {
                return true;
            }
        }
        return false;
    }


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        if (isTestEnvironment()) {
            filterChain.doFilter(request, response);
            return;
        }

//        String timestampStr = request.getHeader("timestamp");
//
//        if (StrUtil.isEmpty(timestampStr)) {
//            writeErrorMessage(response, "timestamp为空，在调用的时候确保将timestamp加入到header中!");
//            return;
//        }
//        long timestamp = Long.parseLong(timestampStr);
//        if ((System.currentTimeMillis() - timestamp) > 2 * 60 * 1000) {
//            writeErrorMessage(response, "请求已过期!");
//            return;
//        }
//        String clientCode = request.getHeader("clientCode");
//        if (StrUtil.isEmpty(clientCode)) {
//            writeErrorMessage(response, "clientId为空，在调用的时候确保将clientCode加入到header中!");
//            return;
//        }
//        String sign = request.getHeader("sign");
//        if (StrUtil.isEmpty(sign)) {
//            writeErrorMessage(response, "sign为空，在调用的时候确保将sign加入到header中!");
//            return;
//        }
//
//        if (!platformAuthenticator.authenticate(timestamp, clientCode, sign)) {
//            writeErrorMessage(response, "认证失败");
//            return;
//        }
        filterChain.doFilter(request, response);
    }

    private void writeErrorMessage(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/plain");
        PrintWriter out = response.getWriter();
        out.println(message);
        out.flush();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
