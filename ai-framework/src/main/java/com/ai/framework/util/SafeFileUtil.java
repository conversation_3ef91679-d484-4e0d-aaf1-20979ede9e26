package com.ai.framework.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.TimeUnit;

/**
 * 安全的文件操作工具类
 * 解决文件名编码和文件占用问题
 *
 * <AUTHOR>
 */
@Slf4j
public class SafeFileUtil {

    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 3;

    /**
     * 重试间隔（毫秒）
     */
    private static final long RETRY_INTERVAL_MS = 1000;

    /**
     * 安全删除文件或目录
     */
    public static boolean safeDelete(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            log.warn("文件路径为空，跳过删除");
            return true;
        }

        try {
            // 解码文件路径
            String decodedPath = decodeFilePath(filePath);
            log.debug("删除文件: 原路径={}, 解码后路径={}", filePath, decodedPath);

            File file = new File(decodedPath);
            if (!file.exists()) {
                log.debug("文件不存在，无需删除: {}", decodedPath);
                return true;
            }

            // 尝试删除
            return deleteWithRetry(file);

        } catch (Exception e) {
            log.error("删除文件失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 安全删除文件（File对象）
     */
    public static boolean safeDelete(File file) {
        if (file == null || !file.exists()) {
            return true;
        }

        try {
            return deleteWithRetry(file);
        } catch (Exception e) {
            log.error("删除文件失败: {}", file.getAbsolutePath(), e);
            return false;
        }
    }

    /**
     * 带重试的删除操作
     */
    private static boolean deleteWithRetry(File file) {
        for (int i = 0; i < MAX_RETRY_COUNT; i++) {
            try {
                if (file.isDirectory()) {
                    return deleteDirectoryWithRetry(file);
                } else {
                    return deleteFileWithRetry(file);
                }
            } catch (Exception e) {
                log.warn("删除文件失败，第{}次重试: {}", i + 1, file.getAbsolutePath(), e);
                
                if (i < MAX_RETRY_COUNT - 1) {
                    try {
                        Thread.sleep(RETRY_INTERVAL_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.warn("删除重试被中断");
                        return false;
                    }
                }
            }
        }
        
        log.error("删除文件最终失败: {}", file.getAbsolutePath());
        return false;
    }

    /**
     * 删除文件
     */
    private static boolean deleteFileWithRetry(File file) throws IOException {
        // 方法1: 使用Java NIO
        try {
            Path path = file.toPath();
            Files.deleteIfExists(path);
            log.debug("使用NIO成功删除文件: {}", file.getAbsolutePath());
            return true;
        } catch (Exception e) {
            log.debug("NIO删除失败，尝试其他方法: {}", e.getMessage());
        }

        // 方法2: 释放文件句柄后删除
        try {
            System.gc(); // 建议垃圾回收，释放可能的文件句柄
            Thread.sleep(100);
            
            if (file.delete()) {
                log.debug("使用File.delete()成功删除文件: {}", file.getAbsolutePath());
                return true;
            }
        } catch (Exception e) {
            log.debug("File.delete()删除失败: {}", e.getMessage());
        }

        // 方法3: 标记删除
        try {
            file.deleteOnExit();
            log.warn("文件无法立即删除，已标记为退出时删除: {}", file.getAbsolutePath());
            return true;
        } catch (Exception e) {
            log.debug("标记删除失败: {}", e.getMessage());
        }

        throw new IOException("无法删除文件: " + file.getAbsolutePath());
    }

    /**
     * 删除目录
     */
    private static boolean deleteDirectoryWithRetry(File directory) throws IOException {
        try {
            // 先删除目录内容
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (!deleteWithRetry(file)) {
                        log.warn("删除目录内文件失败: {}", file.getAbsolutePath());
                    }
                }
            }

            // 删除空目录
            if (directory.delete()) {
                log.debug("成功删除目录: {}", directory.getAbsolutePath());
                return true;
            } else {
                // 标记删除
                directory.deleteOnExit();
                log.warn("目录无法立即删除，已标记为退出时删除: {}", directory.getAbsolutePath());
                return true;
            }
        } catch (Exception e) {
            throw new IOException("删除目录失败: " + directory.getAbsolutePath(), e);
        }
    }

    /**
     * 解码文件路径
     */
    private static String decodeFilePath(String filePath) {
        try {
            // 处理URL编码的文件名
            if (filePath.contains("%")) {
                String decoded = URLDecoder.decode(filePath, StandardCharsets.UTF_8.name());
                log.debug("文件路径解码: {} -> {}", filePath, decoded);
                return decoded;
            }
            return filePath;
        } catch (Exception e) {
            log.warn("文件路径解码失败，使用原路径: {}", filePath, e);
            return filePath;
        }
    }

    /**
     * 安全创建目录
     */
    public static boolean safeMkdirs(String dirPath) {
        try {
            String decodedPath = decodeFilePath(dirPath);
            File dir = new File(decodedPath);
            
            if (dir.exists()) {
                return dir.isDirectory();
            }
            
            boolean created = dir.mkdirs();
            if (created) {
                log.debug("成功创建目录: {}", decodedPath);
            } else {
                log.warn("创建目录失败: {}", decodedPath);
            }
            return created;
            
        } catch (Exception e) {
            log.error("创建目录异常: {}", dirPath, e);
            return false;
        }
    }

    /**
     * 检查文件是否被占用
     */
    public static boolean isFileInUse(File file) {
        try {
            if (!file.exists()) {
                return false;
            }

            // 尝试重命名文件来检查是否被占用
            File tempFile = new File(file.getParent(), file.getName() + ".tmp");
            boolean renamed = file.renameTo(tempFile);
            
            if (renamed) {
                // 重命名成功，说明文件没有被占用，恢复原名
                tempFile.renameTo(file);
                return false;
            } else {
                // 重命名失败，可能被占用
                return true;
            }
        } catch (Exception e) {
            log.debug("检查文件占用状态异常: {}", file.getAbsolutePath(), e);
            return true; // 异常时假设被占用
        }
    }

    /**
     * 等待文件释放
     */
    public static boolean waitForFileRelease(File file, long timeoutMs) {
        long startTime = System.currentTimeMillis();
        
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            if (!isFileInUse(file)) {
                return true;
            }
            
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        return false;
    }

    /**
     * 安全清理目录
     */
    public static void safeCleanDirectory(String dirPath) {
        try {
            String decodedPath = decodeFilePath(dirPath);
            File dir = new File(decodedPath);
            
            if (!dir.exists() || !dir.isDirectory()) {
                log.debug("目录不存在或不是目录: {}", decodedPath);
                return;
            }

            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (!safeDelete(file)) {
                        log.warn("清理目录时删除文件失败: {}", file.getAbsolutePath());
                    }
                }
            }
            
            log.debug("目录清理完成: {}", decodedPath);
            
        } catch (Exception e) {
            log.error("清理目录异常: {}", dirPath, e);
        }
    }
}
