# 视频处理功能实现说明

## 概述
根据 `CreateFileService.dealSuccess` 方法中的注释，我已经补全了视频处理的逻辑。当 `type == 1` 时，系统会处理视频文件。

## 实现的功能

### 1. 主要处理流程
在 `dealSuccess` 方法中，当 `historyVo.getType() == 1` 时：
- 获取视频URL列表
- 调用 `getVideoFrameMessages` 方法处理视频
- 如果处理失败，调用 `dealFailed` 方法
- 如果处理成功，发送成功消息到MQ

### 2. 视频处理方法 (`getVideoFrameMessages`)
- **多线程处理**：使用 `CompletableFuture` 并行处理多个视频文件
- **下载视频**：
  - 首先尝试从本地获取视频文件
  - 如果本地没有，则从URL下载视频文件
- **帧提取**：调用 `extractVideoFrame` 方法提取首帧和尾帧
- **帧处理**：调用 `processVideoFrame` 方法处理每一帧
- **上传到COS**：将处理后的帧图片上传到腾讯云COS

### 3. 视频帧提取方法 (`extractVideoFrame`)
使用 FFmpeg 命令行工具提取视频帧：
- **提取第一帧**：使用 `ffmpeg -i input.mp4 -vf "select=eq(n\,0)" -vframes 1 output.png`
- **提取最后一帧**：使用 `ffmpeg -sseof -1 -i input.mp4 -vframes 1 output.png`
- **错误处理**：检查FFmpeg进程的退出码和输出文件

### 4. 视频帧处理方法 (`processVideoFrame`)
对提取的视频帧进行处理：
- **鉴黄检测**：调用 `ndPicture` 方法进行内容审核
- **敏感内容处理**：如果检测到敏感内容，生成马赛克图片
- **缩略图生成**：生成多种质量的缩略图（15%、30%、70%、90%）
- **图片信息设置**：设置图片的宽高、大小、URL等信息

## 技术特点

### 1. 异步处理
- 使用线程池 `EXECUTOR` 进行异步处理
- 支持多个视频文件的并行处理

### 2. 错误处理
- 完善的异常捕获和日志记录
- 失败时自动清理临时文件
- 处理失败时调用 `dealFailed` 方法通知上游

### 3. 资源管理
- 自动清理下载的视频文件和提取的帧文件
- 使用 `finally` 块确保资源释放

### 4. 兼容性
- 复用现有的图片处理逻辑（鉴黄、缩略图生成、上传等）
- 使用现有的数据结构 `BackendCallBackParams.ImgMessage`
- 保持与图片处理相同的消息格式

## 依赖要求

### 1. FFmpeg
系统需要安装 FFmpeg 命令行工具，确保在系统PATH中可以访问 `ffmpeg` 命令。

### 2. 现有依赖
- 复用了现有的图片处理工具类 `FileUtils`
- 使用了现有的鉴黄API `NdClientApi`
- 使用了现有的COS上传服务 `CosCommonService`

## 使用场景
当ComfyUI生成视频内容时，系统会：
1. 自动提取视频的首帧和尾帧
2. 对提取的帧进行内容审核
3. 生成不同质量的缩略图
4. 上传到云存储
5. 返回处理结果给调用方

这样可以为视频内容提供预览图片，同时确保内容安全性。
