# 视频处理功能实现说明（已更新）

## 概述
根据用户需求，我已经重新设计了视频处理逻辑。当 `type == 1` 时，系统会处理视频文件，并根据鉴黄结果采用不同的处理策略。

## 核心需求实现

### 🎯 主要处理逻辑
1. **NSFW检测策略**：如果首帧或尾帧任一为NSFW，则整体视频鉴黄不通过
2. **NSFW处理**：鉴黄不通过时，只上传一张马赛克图片，所有图片字段使用相同URL
3. **安全内容处理**：鉴黄通过时，添加视频链接字段，并上传首帧的压缩图片

### 🔧 数据结构扩展
在 `BackendCallBackParams.ImgMessage` 中新增：
```java
@Schema(description = "视频文件链接")
private String video_url;
```

## 实现的功能

### 1. 主要处理流程
在 `dealSuccess` 方法中，当 `historyVo.getType() == 1` 时：
- 获取视频URL列表
- 调用 `getVideoFrameMessages` 方法处理视频
- 如果处理失败，调用 `dealFailed` 方法
- 如果处理成功，发送成功消息到MQ

### 2. 视频处理方法 (`getVideoFrameMessages`) - 已重构
- **多线程处理**：使用 `CompletableFuture` 并行处理多个视频文件
- **视频上传**：首先将原始视频上传到COS，获取视频链接
- **帧提取**：提取首帧和尾帧进行鉴黄检测
- **智能处理**：根据鉴黄结果选择不同的处理策略
- **返回单一消息**：每个视频返回一个 `ImgMessage` 对象

### 3. 鉴黄检测逻辑
```java
// 对首帧和尾帧分别进行鉴黄检测
String firstFrameNdResult = ndPicture(firstFrameFile);
String lastFrameNdResult = ndPicture(lastFrameFile);

// 任一帧为NSFW则整体不通过
boolean hasNsfw = StrUtil.isNotBlank(firstFrameNdResult) || StrUtil.isNotBlank(lastFrameNdResult);
```

### 4. NSFW内容处理
当检测到敏感内容时：
- 生成马赛克图片
- 设置敏感标记：`imgMessage.setSensitive(SensitiveMessage.NSFW.getValue())`
- 所有图片字段使用同一张马赛克图片URL
- 仍然保留视频链接

### 5. 安全内容处理 (`processVideoFrameForSafeContent`)
当内容安全时：
- 对首帧生成多种质量的缩略图（15%、30%、70%、90%）
- 设置不同的图片URL字段
- 保留视频链接

### 6. 视频帧提取方法 (`extractVideoFrame`) - 保持不变
使用 FFmpeg 命令行工具提取视频帧：
- **提取第一帧**：使用 `ffmpeg -i input.mp4 -vf "select=eq(n\,0)" -vframes 1 output.png`
- **提取最后一帧**：使用 `ffmpeg -sseof -1 -i input.mp4 -vframes 1 output.png`
- **错误处理**：检查FFmpeg进程的退出码和输出文件

## 技术特点

### 1. 智能鉴黄策略
- **双帧检测**：同时检测首帧和尾帧
- **严格标准**：任一帧为NSFW则整体不通过
- **统一处理**：NSFW内容使用统一的马赛克图片

### 2. 灵活的内容处理
- **NSFW内容**：所有图片字段使用同一张马赛克图片
- **安全内容**：生成多种质量的首帧缩略图
- **视频链接**：无论是否NSFW都保留原始视频链接

### 3. 异步处理
- 使用线程池 `EXECUTOR` 进行异步处理
- 支持多个视频文件的并行处理
- 每个视频返回单一的 `ImgMessage` 对象

### 4. 错误处理
- 完善的异常捕获和日志记录
- 失败时自动清理临时文件
- 处理失败时调用 `dealFailed` 方法通知上游

### 5. 资源管理
- 自动清理下载的视频文件和提取的帧文件
- 使用 `finally` 块确保资源释放
- 及时删除临时生成的马赛克文件

## 依赖要求

### 1. FFmpeg
系统需要安装 FFmpeg 命令行工具，确保在系统PATH中可以访问 `ffmpeg` 命令。

### 2. 现有依赖
- 复用了现有的图片处理工具类 `FileUtils`
- 使用了现有的鉴黄API `NdClientApi`
- 使用了现有的COS上传服务 `CosCommonService`

## 使用场景
当ComfyUI生成视频内容时，系统会：
1. 上传原始视频到云存储
2. 提取视频的首帧和尾帧
3. 对两帧进行内容审核
4. 根据审核结果选择处理策略：
   - **有敏感内容**：返回马赛克图片 + 视频链接
   - **内容安全**：返回首帧缩略图 + 视频链接
5. 返回统一格式的处理结果

这样既保证了内容安全性，又为视频提供了合适的预览图片。

## 新增方法说明

### `processVideoFrameForSafeContent`
专门处理安全内容的首帧：
- 生成15%、30%、70%、90%四种质量的缩略图
- 设置图片的宽高、大小信息
- 分别设置不同质量图片的URL字段

### 修改的返回值
- 原来：每个视频返回多个 `ImgMessage`（首帧+尾帧）
- 现在：每个视频返回单个 `ImgMessage`（包含视频链接和预览图）
